package com.teyuntong.goods.search.service.rpc.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportShieldService;
import com.teyuntong.goods.search.service.biz.exposure.converter.ExposureConverter;
import com.teyuntong.goods.search.service.biz.exposure.service.TransportExposureService;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportVaryRpcConverter;
import com.teyuntong.goods.search.service.biz.goods.dto.*;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.es.service.EsTransportService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportExtendService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainExtendService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportVaryService;
import com.teyuntong.goods.search.service.biz.goods.vo.*;
import com.teyuntong.goods.search.service.biz.search.dto.SearchDistanceDTO;
import com.teyuntong.goods.search.service.biz.search.service.SearchDistanceConfigService;
import com.teyuntong.goods.search.service.common.enums.EsOrDbEnum;
import com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.bidata.BiDataRemoteService;
import com.teyuntong.goods.search.service.remote.goods.ThPriceRemoteService;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchHallRpcService;
import com.teyuntong.goods.service.client.transport.vo.GetEarliestPriceReq;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.inner.export.service.client.bidata.dto.BiRecommendReq;
import com.teyuntong.inner.export.service.client.bidata.vo.BiRecommendVo;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/7/18 19:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SearchHallRpcServiceImpl implements SearchHallRpcService {

    private final EsTransportService esTransportService;

    private final TransportService transportService;

    private final TransportMainExtendService transportMainExtendService;

    private final TransportExtendService transportExtendService;

    private final BiDataRemoteService bidataRemoteService;

    private final TransportExposureService transportExposureService;

    private final ConfigRemoteService configRemoteService;

    private final TransportVaryService transportVaryService;

    private final ShieldingShipperService shieldingShipperService;

    private final SearchDistanceConfigService searchDistanceConfigService;

    private final ABTestRemoteService abTestRemoteService;

    private final TransportShieldService transportShieldService;

    private final ThPriceRemoteService thPriceRemoteService;


    /**
     * 查询前做一些特殊处理，兼容APP入参。
     *
     * @param isBanner 是否是轮播接口
     */
    @Override
    public void beforeSearch(BaseTransportSearchDTO searchDTO, boolean isBanner) {

        // 查询前的前置处理
        searchDTO.preHandle();
        // 当前登录人，未登录也可以看到急走轮播
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        searchDTO.setUserId(loginUser != null ? loginUser.getUserId() : searchDTO.getUserId());
        // 屏蔽货主
        searchDTO.setExcludeUserIds(shieldingShipperService.getShieldingUserList(searchDTO.getUserId()));
        // 屏蔽货源
        List<Long> shieldSrcMsgIds = transportShieldService.getShieldSrcMsgIds(searchDTO.getUserId());
        if (CollUtil.isNotEmpty(searchDTO.getExcludeSrcMsgIds())) {
            shieldSrcMsgIds.addAll(searchDTO.getExcludeSrcMsgIds());
        }
        searchDTO.setExcludeSrcMsgIds(shieldSrcMsgIds);

        // 防止APP乱传
        if (isBanner) {
            searchDTO.setPageSize(10); // 轮播图只取10条
            searchDTO.setQueryType(null);
            searchDTO.setQuerySign(null);
        } else {
            searchDTO.setMaxTsId(null);
            searchDTO.setMinTsId(null);
        }
        // 6620找货大厅新加的
        if (searchDTO.getSearchHallExtra().getMaxTsId() != null) {
            searchDTO.setMaxTsId(searchDTO.getSearchHallExtra().getMaxTsId());
        }
        if (searchDTO.getSearchHallExtra().getMinTsId() != null) {
            searchDTO.setMinTsId(searchDTO.getSearchHallExtra().getMinTsId());
        }
    }


    @Override
    public IntelligenceSortVO intelligenceSortList(IntelligenceSortDTO intelligenceSortDTO) {

        IntelligenceSortVO intelligenceSortVO = new IntelligenceSortVO();
        EsSearchExtra esSearchExtra = intelligenceSortDTO.getEsSearchExtra();

        if (esSearchExtra.getFirstSearchTime() == null) {
            esSearchExtra.setFirstSearchTime(System.currentTimeMillis());
        }
        intelligenceSortVO.setEsSearchExtra(esSearchExtra);
        // 前置数据处理
        beforeSearch(intelligenceSortDTO, false);

        List<TransportEsVO> transportEsVOList = esTransportService.intelligenceSortList(intelligenceSortDTO);
        if (CollUtil.isNotEmpty(transportEsVOList)) {
            TransportEsVO lastTransportEsVO = transportEsVOList.get(transportEsVOList.size() - 1);

            esSearchExtra.setLastSortTsId(lastTransportEsVO.getId());
            esSearchExtra.setLastSortScore(lastTransportEsVO.getScore());
            intelligenceSortVO.setHasNext(transportEsVOList.size() >= intelligenceSortDTO.getPageSize());

            List<TransportEsDO> transportEsDOList = transportEsVOList.stream().map(TransportEsVO::getTransportEsDO).toList();

            List<TransportVO> transportVOList = TransportConverter.INSTANCE.convertEsDOs2VOs(transportEsDOList);

            //如果是相似货源，打上标识，客户端是否展示该相似货源
            transportService.handleSimilarTransport(transportVOList, intelligenceSortDTO.getUserId());
            // 对货源进行打标
            transportService.handleTransportTag(transportVOList, intelligenceSortDTO.getUserId());
            // 加密货源字段，会把userId设为默认userId
            transportService.hideSensitiveInfo(transportVOList, intelligenceSortDTO.getUserId());

            // 插入固定位置的货源
            transportService.handleFixedTransport(intelligenceSortDTO, transportVOList, intelligenceSortDTO.getUserId());


            intelligenceSortVO.setList(transportVOList);
        }

        return intelligenceSortVO;

    }

    @Override
    public IntelligenceCountVO intelligenceBubbleCount(IntelligenceSortDTO intelligenceSortDTO) {


        IntelligenceCountVO intelligenceCountVO = new IntelligenceCountVO();


        EsSearchExtra esSearchExtra = intelligenceSortDTO.getEsSearchExtra();
        if (esSearchExtra.getFirstSearchTime() == null) {
            esSearchExtra.setFirstSearchTime(System.currentTimeMillis());
        }
        // 处理数据
        beforeSearch(intelligenceSortDTO, false);


        Long count = esTransportService.intelligenceBubbleCount(intelligenceSortDTO);

        intelligenceCountVO.setBubbleCount(count);
        intelligenceCountVO.setEsSearchExtra(esSearchExtra);
        return intelligenceCountVO;
    }

    @Override
    public List<TransportVO> searchHallList(BaseTransportSearchDTO searchDTO) {

        // 查询前的前置处理
        beforeSearch(searchDTO, false);

        List<TransportVO> transportVOList;
        // 获取走es还是走db配置
        EsOrDbEnum esOrDbEnum = transportService.getEsDbEnum(searchDTO.getUserId());

        if (esOrDbEnum == EsOrDbEnum.ES) {
            List<TransportEsDO> transportEsDOList = esTransportService.searchHallList(searchDTO);
            transportVOList = TransportConverter.INSTANCE.convertEsDOs2VOs(transportEsDOList);
        } else {
            // 查询货源列表
            List<TransportDO> transportDOList = transportService.searchHallList(searchDTO);
            transportVOList = TransportConverter.INSTANCE.convertDOs2VOs(transportDOList);
            if (CollUtil.isEmpty(transportDOList)) {
                return List.of();
            }
            // 设置扩展表字段
            List<Long> tsIdList = transportDOList.stream().map(TransportDO::getId).toList();
            Map<Long, TransportExtendDO> extendDOMap = transportExtendService.getMapByTsIds(tsIdList);
            transportVOList.forEach(vo -> TransportConverter.INSTANCE.transportExtend2VO(vo, extendDOMap.get(vo.getId())));
        }
        //如果是相似货源，打上标识，客户端是否展示该相似货源
        transportService.handleSimilarTransport(transportVOList, searchDTO.getUserId());
        // 对货源进行打标
        transportService.handleTransportTag(transportVOList, searchDTO.getUserId());
        // 加密货源字段
        transportService.hideSensitiveInfo(transportVOList, searchDTO.getUserId());

        // 好货推荐ab测，后面可以下掉 （整体按照73开，实验组7，对照组3，按照货源ID最后一位决定）
        if (Objects.equals(searchDTO.getGoodTransportRecommend(), 1)) {
            if (configRemoteService.getIntValue("good_transport_recommend_abtest", 0) == 1) {
                transportVOList.removeIf(t -> t.getSrcMsgId() % 10 > 6);
            }
        }

        return transportVOList;
    }

    @Override
    public SearchHallVO searchHallNewList(BaseTransportSearchDTO searchDTO) {

        SearchHallVO searchHallVo = new SearchHallVO();
        searchHallVo.setSearchHallExtra(searchDTO.getSearchHallExtra());

        List<TransportVO> transportVOList = this.searchHallList(searchDTO);

        if (CollUtil.isNotEmpty(transportVOList)) {
            searchHallVo.setHasNext(transportVOList.size() >= searchDTO.getPageSize());
            // 获取transportVOList元素中id最大的元素
            Long maxTsId = transportVOList.get(0).getId();
            Long minTsId = transportVOList.get(transportVOList.size() - 1).getId();
            searchHallVo.setMaxTsId(maxTsId);
            searchHallVo.setSearchHallExtra(SearchHallExtra.builder().maxTsId(maxTsId).minTsId(minTsId).build());
            // 插入固定位置的货源
            transportService.handleFixedTransport(searchDTO, transportVOList, searchDTO.getUserId());

            searchHallVo.setResponseSize(transportVOList.size());
        }
        searchHallVo.setList(transportVOList);

        return searchHallVo;
    }

    @Override
    public SearchHallCountVO searchHallCount(SearchHallCountDTO searchDTO) {

        SearchHallCountVO searchHallCountVO = new SearchHallCountVO();
        // 时间排序气泡接口的最大货源id参数不允许为空
        if (searchDTO.getMaxTsId() == null || searchDTO.getMaxTsId() <= 0) {
            return searchHallCountVO;
        }
        searchDTO.getSearchHallExtra().setMaxTsId(searchDTO.getMaxTsId());
        // 处理数据
        beforeSearch(searchDTO, false);

        List<TransportVO> transportVOList = new ArrayList<>();
        // 获取走es还是走db配置
        EsOrDbEnum esOrDbEnum = transportService.getEsDbEnum(searchDTO.getUserId());
        Long bubbleCount = 0L;
        if (esOrDbEnum == EsOrDbEnum.ES) {
            bubbleCount = esTransportService.searchHallCount(searchDTO);
            if (bubbleCount > 0) {
                searchDTO.setPageSize(1);
                List<TransportEsDO> transportEsDOList = esTransportService.searchHallCountDetail(searchDTO);
                transportVOList = TransportConverter.INSTANCE.convertEsDOs2VOs(transportEsDOList);
            }
        } else {
            bubbleCount = transportService.searchHallCount(searchDTO);
            if (bubbleCount > 0) {
                List<TransportDO> transportDOList = transportService.searchHallCountDetail(searchDTO);
                transportVOList = TransportConverter.INSTANCE.convertDOs2VOs(transportDOList);
            }
        }
        if (CollUtil.isNotEmpty(transportVOList)) {
            //如果是相似货源，打上标识，客户端是否展示该相似货源
            transportService.handleSimilarTransport(transportVOList, searchDTO.getUserId());
            // 对货源进行打标
            transportService.handleTransportTag(transportVOList, searchDTO.getUserId());
            // 加密货源字段
            transportService.hideSensitiveInfo(transportVOList, searchDTO.getUserId());

        }
        searchHallCountVO.setTransportVOList(transportVOList);
        searchHallCountVO.setMaxTsId(CollUtil.isNotEmpty(transportVOList) ? transportVOList.get(0).getId() : searchDTO.getMaxTsId());
        searchHallCountVO.setBubbleCount(bubbleCount);
        return searchHallCountVO;


    }

    @Override
    public List<TransportVO> recommendList(HallRecommendDTO hallRecommendDTO) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            hallRecommendDTO.setUserId(loginUser.getUserId());
        }
        BiRecommendReq biRecommendReq = new BiRecommendReq();
        BeanUtil.copyProperties(hallRecommendDTO, biRecommendReq);
        // 调用推荐接口获取货源id
        ResultMsgBean<List<BiRecommendVo>> resultMsgBean;
        try {
            resultMsgBean = bidataRemoteService.recommendList(biRecommendReq);
        } catch (Exception e) {
            log.error("推荐接口返回异常，请求参数：{}", JSONUtil.toJsonStr(biRecommendReq), e);
            return List.of();
        }
        return afterRecommend(resultMsgBean, hallRecommendDTO.getUserId());
    }


    @Override
    public List<TransportVO> intelligenceRecommendList(IntelligenceRecommendDTO intelligenceRecommendDTO) {
        if (intelligenceRecommendDTO == null) {
            intelligenceRecommendDTO = new IntelligenceRecommendDTO();
        }
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            intelligenceRecommendDTO.setUserId(loginUser.getUserId());
        }
        BiRecommendReq biRecommendReq = new BiRecommendReq();
        BeanUtil.copyProperties(intelligenceRecommendDTO, biRecommendReq);
        // 调用推荐接口获取货源id
        ResultMsgBean<List<BiRecommendVo>> resultMsgBean = null;
        try {
            resultMsgBean = bidataRemoteService.intelligenceRecommendList(biRecommendReq);
        } catch (IOException e) {
            log.error("推荐接口返回异常，请求参数：{}", JSONUtil.toJsonStr(biRecommendReq), e);
            return List.of();
        }
        return afterRecommend(resultMsgBean, intelligenceRecommendDTO.getUserId());
    }

    /**
     * 推荐货源调用bi接口后后续处理
     *
     * @param resultMsgBean
     * @param userId
     * @return
     */
    @Override
    public List<TransportVO> afterRecommend(ResultMsgBean<List<BiRecommendVo>> resultMsgBean, Long userId) {
        if (resultMsgBean == null || !resultMsgBean.isSuccess()) {
            log.error("推荐接口返回数据异常,,response:{}", JSONUtil.toJsonStr(resultMsgBean));
            return List.of();
        }
        List<BiRecommendVo> biRecommendVoList = resultMsgBean.getData();
        if (CollUtil.isEmpty(biRecommendVoList)) {
            return List.of();
        }

        // 过滤掉无效货源id和屏蔽掉的货源
        List<Long> shieldSrcMsgIds = transportShieldService.getShieldSrcMsgIds(userId);
        List<Long> srdMsgIdList = biRecommendVoList.stream().map(BiRecommendVo::getSrcMsgId)
                .filter(Objects::nonNull).filter(t -> !shieldSrcMsgIds.contains(t)).toList();
        if (CollUtil.isEmpty(srdMsgIdList)) {
            return List.of();
        }

        // 根据货源id查询货源信息
        List<TransportVO> transportVOList;
        // 获取走es还是走db配置
        EsOrDbEnum esOrDbEnum = transportService.getEsDbEnum(userId);
        if (esOrDbEnum == EsOrDbEnum.ES) {
            List<TransportEsDO> transportEsDOList = esTransportService.selectBySrcMsgIds(srdMsgIdList);
            transportVOList = TransportConverter.INSTANCE.convertEsDOs2VOs(transportEsDOList);
        } else {
            List<TransportDO> transportDOList = transportService.selectBySrcMsgIdsAndStatus(srdMsgIdList);
            transportVOList = TransportConverter.INSTANCE.convertDOs2VOs(transportDOList);
            if (CollUtil.isEmpty(transportDOList)) {
                return List.of();
            }
            // 设置扩展表字段
            List<Long> tsIdList = transportDOList.stream().map(TransportDO::getId).toList();
            Map<Long, TransportExtendDO> extendDOMap = transportExtendService.getMapByTsIds(tsIdList);
            transportVOList.forEach(vo -> TransportConverter.INSTANCE.transportExtend2VO(vo, extendDOMap.get(vo.getId())));
        }
        if (CollUtil.isEmpty(transportVOList)) {
            return List.of();
        }

        // 过滤测试账号
        transportVOList = transportService.filterTest(transportVOList, userId);

        //如果是相似货源，打上标识，客户端是否展示该相似货源
        transportService.handleSimilarTransport(transportVOList, userId);
        // 对货源进行打标
        transportService.handleTransportTag(transportVOList, userId);
        // 加密货源字段
        transportService.hideSensitiveInfo(transportVOList, userId);

        // 将srcMsgId赋值给tsId，是因为客户端有bug，等客户端详情都切换到新接口时，这个赋值可以去掉
        transportVOList.forEach(t -> t.setTsId(t.getSrcMsgId()));

        return transportVOList;
    }

    @Override
    public List<TransportVaryVO> searchHallVary(TransportVaryDTO transportVaryDTO) {
        if (transportVaryDTO.getMaxId() == null || transportVaryDTO.getMaxId() <= 0L) {
            transportVaryDTO.setPageSize(1);
        }

        List<TransportVaryDO> transportVaryDOList = transportVaryService.searchHallVary(transportVaryDTO);
        if (transportVaryDTO.getMaxId() == null || transportVaryDTO.getMaxId() <= 0L) {
            transportVaryDOList.forEach(t -> t.setTsId(0L));
        }
        return TransportVaryRpcConverter.INSTANCE.convertDOs2VOs(transportVaryDOList);
    }

    @Override
    public List<TransportVO> searchShortRange(ShortRangeDTO shortRangeDTO) {
        String startCoord = shortRangeDTO.getStartCoord();
        String startDistance = shortRangeDTO.getStartDistance();
        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();
        BeanUtil.copyProperties(shortRangeDTO, baseTransportSearchDTO);
        // 范围倒短，出发地城市固定，目的地在出发地的范围内进行查询
        baseTransportSearchDTO.setDestCoord(startCoord);
        baseTransportSearchDTO.setDestDistance(startDistance);
        baseTransportSearchDTO.setStartCoord(null);
        baseTransportSearchDTO.setStartDistance(null);
        if (baseTransportSearchDTO.getStartCity().equals(baseTransportSearchDTO.getStartArea())) {
            baseTransportSearchDTO.setStartArea(null);
        }
        return this.searchHallList(baseTransportSearchDTO);

    }

    @Override
    public List<TransportVO> searchShortProvince(ShortProvinceDTO shortProvinceDTO) {
        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();
        BeanUtil.copyProperties(shortProvinceDTO, baseTransportSearchDTO);
        // 省内倒短，出发地目的地都为同一个省份
        baseTransportSearchDTO.setDestProvinc(shortProvinceDTO.getStartProvinc());
        return this.searchHallList(baseTransportSearchDTO);
    }

    @Override
    public List<ExposureVO> searchRouteDestList(SameRouteDestDTO sameRouteDestDTO) {
        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();
        BeanUtil.copyProperties(sameRouteDestDTO, baseTransportSearchDTO);
//        // 同目的地同路线货源不能包含当前详情页的货源，要排除掉
        if (sameRouteDestDTO.getGoodsId() != null) {
            baseTransportSearchDTO.setExcludeSrcMsgIds(new ArrayList<>(List.of(sameRouteDestDTO.getGoodsId())));
        }
        List<TransportVO> transportVOList = this.searchHallList(baseTransportSearchDTO);
        List<ExposureVO> exposureVOList = ExposureConverter.INSTANCE.convertTsVOs2VOs(transportVOList);
        // 如果这个列表中有急走货源，就把其中的一条急走货源拿出来放到第一条
        if (CollUtil.isNotEmpty(exposureVOList)) {
            // 将srcMsgId赋值给tsId，是因为客户端有bug，等客户端详情都切换到新接口时，这个赋值可以去掉
            exposureVOList.forEach(t -> t.setTsId(t.getSrcMsgId()));

            TransportDO transportDO = transportExposureService.getExposureOfScore(baseTransportSearchDTO);
            if (transportDO != null) {
                //移除包含急走货源的那条数据
                exposureVOList.removeIf(list -> list.getSrcMsgId().equals(transportDO.getSrcMsgId()));
                ExposureVO exposureVO = ExposureConverter.INSTANCE.convertTsDO2VO(transportDO);

                Long userId = null;
                try {
                    userId = LoginHelper.getRequiredLoginUser().getUserId();
                    Integer userType = abTestRemoteService.getUserType("not_show_transport_user_data", userId);
                    if (userType != null && userType == 1) {
                        exposureVO.setNickName("特运通老板");
                    }
                } catch (Exception e) {

                }
                // 整车拼车字段处理
                TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(transportDO.getSrcMsgId());
                if (Objects.nonNull(mainExtendDO)) {
                    exposureVO.setUseCarType(mainExtendDO.getUseCarType());
                }

                if (StringUtils.isNotBlank(exposureVO.getPrice())) {
                    Map<Long, BigDecimal> transportEarliestPriceMap = thPriceRemoteService.getTransportEarliestPrice(new GetEarliestPriceReq(Arrays.asList(exposureVO.getSrcMsgId())));

                    BigDecimal priceDec = new BigDecimal(exposureVO.getPrice());
                    // 当前运费与最开始有运费时的运费比较加价了多少钱
                    BigDecimal transportEarliestPrice = transportEarliestPriceMap.get(exposureVO.getSrcMsgId());
                    if (transportEarliestPrice != null && transportEarliestPrice.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal subtract = priceDec.subtract(transportEarliestPrice);
                        if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                            exposureVO.setAddPriceNum(subtract);
                        }
                    }

                    exposureVO.setActualPrice(exposureVO.getPrice());
                    // 到手运费 = 发货填写的运费 - 不退还定金 + 平台补贴
                    BigDecimal actualPrice = priceDec;
                    if (exposureVO.getRefundFlag() != null && exposureVO.getRefundFlag() == 0 && exposureVO.getInfoFee() != null && exposureVO.getInfoFee().compareTo(BigDecimal.ZERO) > 0) {
                        actualPrice = priceDec.subtract(exposureVO.getInfoFee());
                    }
                    if (mainExtendDO != null && mainExtendDO.getPerkPrice() != null) {
                        actualPrice = actualPrice.add(new BigDecimal(mainExtendDO.getPerkPrice()));
                    }
                    if (actualPrice.compareTo(BigDecimal.ZERO) > 0) {
                        exposureVO.setActualPrice(actualPrice.setScale(0, RoundingMode.DOWN).toPlainString());
                    }
                }

                //将急走的放到第一条
                exposureVOList.add(0, exposureVO);
            }
        }
        return exposureVOList;
    }

    /**
     * 返回找货大厅配置的搜索距离，优先匹配市，再匹配省，最后返回默认值。
     * 把入参传过去，防止APP切换城市频繁，返回的距离不是当前城市的配置。
     */
    @Override
    public SearchDistanceDTO getSearchDistance(SearchDistanceDTO sdDTO) {
        Integer distance = searchDistanceConfigService.getOptimalDistance(sdDTO.getProvince(), sdDTO.getCity());
        if (distance == null) {
            distance = configRemoteService.getIntValue("hall_search_distance_default", 100);
        }
        sdDTO.setDistance(distance);
        return sdDTO;
    }

}
